/**
 * 内置 Todo 工具模块
 * 
 * 主要功能：
 * 1. 封装所有 todolist 相关功能，作为内置工具使用
 * 2. 提供统一的工具执行入口，支持所有原 todolist-api 的方法
 * 3. 实现认证状态复用、连接复用等性能优化
 * 4. 保持与原 API 完全兼容的接口和返回格式
 * 
 * 设计原则：
 * - 模块化设计：将原有的 authManager、taskManager、projectManager 整合
 * - 性能优化：实现实例级别的状态复用和缓存机制
 * - 兼容性保证：保持原有接口的完全兼容
 * - 错误处理：统一的错误处理和日志记录
 * 
 * <AUTHOR> 开发团队
 * @version 1.0.0
 * @since 2024-01-01
 */

// 导入原有的配置和工具函数
const { API_CONFIG, TASK_CONFIG, PROJECT_CONFIG, ERROR_CODES } = require('./todo/config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  formatDateForApi,
  simplifyTaskData,
  simplifyProjectData,
  removeEmptyFields,
  parseHttpResponse,
  extractTokenFromCookies
} = require('./todo/utils')
const { taskMethods, projectMethods } = require('./todo/methods')

/**
 * Todo 工具类
 * 封装所有 todolist 相关功能的主类
 */
class TodoTool {
  constructor() {
    // 基础配置
    this.BASE_URL = API_CONFIG.BASE_URL
    this.token = null
    this.headers = {}
    this.debug = true
    
    // 性能优化：缓存机制
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
    this.maxCacheSize = 100 // 最大缓存条目数

    // 性能优化：连接复用
    this.connectionPool = new Map()
    this.maxConnections = 10
    this.connectionTimeout = 30 * 1000 // 30秒连接超时

    // 认证状态
    this.isAuthenticated = false
    this.lastAuthTime = null

    // 性能统计
    this.stats = {
      requestCount: 0,
      cacheHits: 0,
      cacheMisses: 0,
      avgResponseTime: 0,
      lastResetTime: Date.now()
    }
    
    console.log('[TodoTool] 内置 Todo 工具初始化完成')
  }

  /**
   * 统一的工具执行入口
   * @param {string} method - 方法名
   * @param {object} parameters - 参数对象
   * @returns {object} 执行结果
   */
  async execute(method, parameters = {}) {
    console.log(`[TodoTool] [execute] 执行方法：${method}，参数：`, parameters)
    
    try {
      // 确保认证状态
      await this.ensureAuthenticated()
      
      // 根据方法名调用对应的处理函数
      switch (method) {
        // 认证相关方法
        case 'login':
          return await this.login(parameters)
        case 'initWithToken':
          return await this.initWithToken(parameters.token)
        case 'getBatchData':
          return await this.getBatchData()
          
        // 任务管理方法
        case 'getTasks':
          return await this.getTasks(parameters)
        case 'createTask':
          return await this.createTask(parameters)
        case 'updateTask':
          return await this.updateTask(parameters.taskId, parameters.updateData)
        case 'deleteTask':
          return await this.deleteTask(parameters.taskId)
        case 'completeTask':
          return await this.completeTask(parameters.taskId)
        case 'uncompleteTask':
          return await this.uncompleteTask(parameters.taskId)
        case 'getTask':
          return await this.getTask(parameters.taskId)
        case 'batchOperateTasks':
          return await this.batchOperateTasks(parameters)
          
        // 项目管理方法
        case 'getProjects':
          return await this.getProjects(parameters)
        case 'createProject':
          return await this.createProject(parameters)
        case 'updateProject':
          return await this.updateProject(parameters.projectId, parameters.updateData)
        case 'deleteProject':
          return await this.deleteProject(parameters.projectId)
        case 'closeProject':
          return await this.closeProject(parameters.projectId)
        case 'reopenProject':
          return await this.reopenProject(parameters.projectId)
        case 'getProject':
          return await this.getProject(parameters.projectId)
          
        default:
          throw new Error(`未知的方法：${method}`)
      }
    } catch (error) {
      console.error(`[TodoTool] [execute] 方法 ${method} 执行失败：`, error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message, error)
    }
  }

  /**
   * 确保认证状态
   * 实现认证状态复用，避免重复认证
   */
  async ensureAuthenticated() {
    // 如果已认证且未过期，直接返回
    if (this.isAuthenticated && this.lastAuthTime && 
        (Date.now() - this.lastAuthTime < 30 * 60 * 1000)) { // 30分钟有效期
      return
    }
    
    // 如果有调试 token，使用调试 token
    if (this.debug) {
      const debugToken = '73AE2E6CC13DD9673F421A1F3E02AED0E1BFB595FD663AFA63ED00682C85E0350ECBA76C0D9169C1842C895EC3C7FD43FA4BB3D094DAFA93E6FC18AA49B4F5302701265667560665A0D14835FCC55972EB9036F52182EC2D6CFEC251B6B3AD83385AA04082B6E13207380EE6E17F65D7D02746F0B1CB9D088DFB1EDE0D3D45D112B6963F72E74B8898CEFB2AD56ED90B75338A509771CA53C093C355F178EA86151002FFD8A51141ED48EB889B07BD4E'
      await this.initWithToken(debugToken)
    }
  }

  /**
   * 用户登录认证
   * @param {object} options - 登录参数
   * @returns {object} 登录结果
   */
  async login(options = {}) {
    const { username, password, isPhone = true } = options
    console.log(`[TodoTool] [login] 开始登录，用户名：${username}，登录方式：${isPhone ? '手机' : '邮箱'}`)
    
    // 参数校验
    const validation = validateParams({ username, password }, ['username', 'password'])
    if (validation) {
      console.warn('[TodoTool] [login] 参数校验失败：', validation)
      return validation
    }

    try {
      const loginUrl = API_CONFIG.LOGIN_URL
      
      // 根据登录类型选择不同的字段名
      const loginData = isPhone ? {
        password: password,
        phone: username
      } : {
        password: password,
        email: username
      }

      const headers = API_CONFIG.LOGIN_HEADERS
      
      console.log('[TodoTool] [login] 发送登录请求...')
      const response = await this._request('POST', loginUrl, loginData, headers)
      
      if (response.errCode) {
        console.error('[TodoTool] [login] 登录失败：', response)
        return response
      }

      // 解析响应获取 token
      const parsedResponse = parseHttpResponse(response)
      if (parsedResponse.errCode) {
        console.error('[TodoTool] [login] 响应解析失败：', parsedResponse)
        return parsedResponse
      }

      // 从 cookies 中提取 token
      const token = extractTokenFromCookies(parsedResponse.headers)
      if (!token) {
        console.error('[TodoTool] [login] 未能从响应中提取到 token')
        return createErrorResponse(ERROR_CODES.TOKEN_NOT_FOUND, '登录成功但未能获取到访问令牌')
      }

      // 保存认证信息
      this.token = token
      this.isAuthenticated = true
      this.lastAuthTime = Date.now()
      this.headers = {
        ...API_CONFIG.DEFAULT_HEADERS,
        Cookie: `t=${token}`
      }

      console.log('[TodoTool] [login] 登录成功，token 已保存')
      return createSuccessResponse('登录成功', { 
        token: token,
        user: parsedResponse.data 
      })

    } catch (error) {
      console.error('[TodoTool] [login] 登录异常：', error)
      return createErrorResponse(ERROR_CODES.LOGIN_ERROR, error.message || '登录失败', error)
    }
  }

  /**
   * 使用已有 token 初始化
   * @param {string} token - 访问令牌
   * @returns {object} 初始化结果
   */
  async initWithToken(token) {
    console.log('[TodoTool] [initWithToken] 使用已有 token 初始化...')
    
    if (!token) {
      return createErrorResponse(ERROR_CODES.PARAM_IS_NULL, 'token 不能为空')
    }

    try {
      // 保存认证信息
      this.token = token
      this.headers = {
        ...API_CONFIG.DEFAULT_HEADERS,
        Cookie: `t=${token}`
      }

      // 验证 token 有效性（通过获取基础数据）
      const batchResult = await this.getBatchData()
      if (batchResult.errCode) {
        console.error('[TodoTool] [initWithToken] token 验证失败：', batchResult)
        this.token = null
        this.headers = {}
        this.isAuthenticated = false
        return createErrorResponse(ERROR_CODES.UNAUTHORIZED, 'token 无效或已过期')
      }

      this.isAuthenticated = true
      this.lastAuthTime = Date.now()
      
      console.log('[TodoTool] [initWithToken] token 初始化成功')
      return createSuccessResponse('token 初始化成功', { token: token })

    } catch (error) {
      console.error('[TodoTool] [initWithToken] 初始化异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || 'token 初始化失败', error)
    }
  }

  /**
   * 获取所有基础数据（任务、项目、标签）
   * 实现缓存机制提高性能
   * @returns {object} 基础数据
   */
  async getBatchData() {
    console.log('[TodoTool] [getBatchData] 开始获取所有基础数据...')

    // 清理过期缓存
    this._cleanExpiredCache()

    // 检查缓存
    const cacheKey = 'batchData'
    const cached = this.cache.get(cacheKey)
    if (cached && (Date.now() - cached.timestamp < this.cacheTimeout)) {
      console.log('[TodoTool] [getBatchData] 使用缓存数据')
      this._updateStats(0, true) // 缓存命中，响应时间为0
      return cached.data
    }

    try {
      const response = await this._request('GET', API_CONFIG.BATCH_DATA_URL)
      const parsedResponse = parseHttpResponse(response)

      if (parsedResponse.errCode) {
        console.error('[TodoTool] [getBatchData] 获取基础数据失败：', parsedResponse)
        return parsedResponse
      }

      const result = createSuccessResponse('获取基础数据成功', parsedResponse.data)

      // 缓存结果
      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      })

      console.log('[TodoTool] [getBatchData] 获取基础数据成功')
      return result

    } catch (error) {
      console.error('[TodoTool] [getBatchData] 获取基础数据异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取基础数据失败', error)
    }
  }

  /**
   * 获取任务列表
   * @param {object} options - 查询参数
   * @returns {object} 任务列表
   */
  async getTasks(options = {}) {
    const {
      mode = "all",
      keyword = null,
      priority = null,
      projectName = null,
      completed = null
    } = options

    console.log('[TodoTool] [getTasks] 开始获取任务列表，过滤条件：', { mode, keyword, priority, projectName, completed })

    try {
      const batchResult = await this.getBatchData()
      if (batchResult.errCode) {
        console.error('[TodoTool] [getTasks] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { tasks, projects } = batchResult.data
      let filteredTasks = []
      console.log(`[TodoTool] [getTasks] 原始任务数：${tasks.length}, 项目数：${projects.length}`)

      // 处理时间筛选
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

      for (const task of tasks) {
        // 只处理文本类型的任务
        if (task.kind !== TASK_CONFIG.KIND.TEXT) continue

        // 时间筛选
        if (mode !== TASK_CONFIG.FILTER_MODE.ALL) {
          const taskDate = task.modifiedTime ? new Date(task.modifiedTime) : null
          if (!taskDate) continue

          const taskDay = new Date(taskDate.getFullYear(), taskDate.getMonth(), taskDate.getDate())

          if (mode === TASK_CONFIG.FILTER_MODE.TODAY && taskDay.getTime() !== today.getTime()) continue
          if (mode === TASK_CONFIG.FILTER_MODE.YESTERDAY && taskDay.getTime() !== yesterday.getTime()) continue
          if (mode === TASK_CONFIG.FILTER_MODE.RECENT_7_DAYS && taskDay < sevenDaysAgo) continue
        }

        // 完成状态筛选
        if (completed !== null) {
          const isCompleted = task.status === TASK_CONFIG.STATUS.COMPLETED
          if (completed !== isCompleted) continue
        }

        // 优先级筛选
        if (priority !== null && task.priority !== priority) continue

        // 关键词筛选
        if (keyword) {
          const searchText = `${task.title || ''} ${task.content || ''}`.toLowerCase()
          if (!searchText.includes(keyword.toLowerCase())) continue
        }

        // 项目名称筛选
        if (projectName) {
          const project = projects.find(p => p.id === task.projectId)
          if (!project || !project.name.toLowerCase().includes(projectName.toLowerCase())) continue
        }

        // 简化任务数据
        const simplifiedTask = simplifyTaskData(task)

        // 添加项目信息
        if (task.projectId) {
          const project = projects.find(p => p.id === task.projectId)
          if (project) {
            simplifiedTask.projectName = project.name
            simplifiedTask.projectColor = project.color
          }
        }

        filteredTasks.push(simplifiedTask)
      }

      console.log(`[TodoTool] [getTasks] 筛选后任务数：${filteredTasks.length}`)
      return createSuccessResponse('获取任务列表成功', filteredTasks)

    } catch (error) {
      console.error('[TodoTool] [getTasks] 获取任务列表异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取任务列表失败', error)
    }
  }

  /**
   * 创建任务
   * @param {object} options - 任务数据
   * @returns {object} 创建结果
   */
  async createTask(options = {}) {
    const {
      title,
      content = '',
      priority = 0,
      projectName = null,
      tagNames = [],
      startDate = null,
      dueDate = null,
      isAllDay = false,
      reminder = null,
      kind = 'TEXT'
    } = options

    console.log(`[TodoTool] [createTask] 开始创建任务，标题：${title}`)

    // 参数校验
    const validation = validateParams({ title }, ['title'])
    if (validation) {
      console.warn('[TodoTool] [createTask] 参数校验失败：', validation)
      return validation
    }

    try {
      // 获取基础数据以查找项目和标签
      const batchResult = await this.getBatchData()
      if (batchResult.errCode) {
        console.error('[TodoTool] [createTask] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { projects, tags } = batchResult.data
      let projectId = null
      let tagIds = []

      // 查找项目 ID
      if (projectName) {
        const project = projects.find(p => p.name.toLowerCase() === projectName.toLowerCase())
        if (project) {
          projectId = project.id
        } else {
          console.warn(`[TodoTool] [createTask] 未找到项目：${projectName}`)
        }
      }

      // 查找标签 ID
      if (tagNames && tagNames.length > 0) {
        for (const tagName of tagNames) {
          const tag = tags.find(t => t.name.toLowerCase() === tagName.toLowerCase())
          if (tag) {
            tagIds.push(tag.id)
          } else {
            console.warn(`[TodoTool] [createTask] 未找到标签：${tagName}`)
          }
        }
      }

      // 准备任务数据
      const taskData = {
        title: title,
        content: content,
        priority: priority,
        kind: kind,
        status: TASK_CONFIG.STATUS.ACTIVE,
        isAllDay: isAllDay
      }

      // 添加可选字段
      if (projectId) taskData.projectId = projectId
      if (tagIds.length > 0) taskData.tags = tagIds
      if (startDate) taskData.startDate = formatDateForApi(startDate)
      if (dueDate) taskData.dueDate = formatDateForApi(dueDate)
      if (reminder) taskData.reminder = reminder

      // 移除空值字段
      const cleanTaskData = removeEmptyFields(taskData)
      console.log('[TodoTool] [createTask] 准备发送的任务数据：', cleanTaskData)

      // 发送创建请求
      const response = await this._request('POST', API_CONFIG.TASK_URL, cleanTaskData)
      const parsedResponse = parseHttpResponse(response)

      if (parsedResponse.errCode) {
        console.error('[TodoTool] [createTask] 创建任务失败：', parsedResponse)
        return parsedResponse
      }

      // 清除缓存
      this.cache.delete('batchData')

      console.log('[TodoTool] [createTask] 任务创建成功')
      return createSuccessResponse('任务创建成功', parsedResponse.data)

    } catch (error) {
      console.error('[TodoTool] [createTask] 创建任务异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '创建任务失败', error)
    }
  }

  /**
   * 更新任务
   * @param {string} taskId - 任务 ID
   * @param {object} updateData - 更新数据
   * @returns {object} 更新结果
   */
  async updateTask(taskId, updateData) {
    console.log(`[TodoTool] [updateTask] 开始更新任务，ID: ${taskId}`, { updateData })

    // 参数校验
    const validation = validateParams({ taskId }, ['taskId'])
    if (validation) {
      console.warn('[TodoTool] [updateTask] 参数校验失败：', validation)
      return validation
    }

    try {
      // 处理项目名称和标签名称
      if (updateData.projectName || updateData.tagNames) {
        const batchResult = await this.getBatchData()
        if (batchResult.errCode) {
          console.error('[TodoTool] [updateTask] 获取基础数据失败：', batchResult)
          return batchResult
        }

        const { projects, tags } = batchResult.data

        // 处理项目名称
        if (updateData.projectName) {
          const project = projects.find(p => p.name.toLowerCase() === updateData.projectName.toLowerCase())
          if (project) {
            updateData.projectId = project.id
          } else {
            console.warn(`[TodoTool] [updateTask] 未找到项目：${updateData.projectName}`)
          }
          delete updateData.projectName
        }

        // 处理标签名称
        if (updateData.tagNames) {
          const tagIds = []
          for (const tagName of updateData.tagNames) {
            const tag = tags.find(t => t.name.toLowerCase() === tagName.toLowerCase())
            if (tag) {
              tagIds.push(tag.id)
            } else {
              console.warn(`[TodoTool] [updateTask] 未找到标签：${tagName}`)
            }
          }
          updateData.tags = tagIds
          delete updateData.tagNames
        }
      }

      // 处理日期格式
      if (updateData.startDate) {
        updateData.startDate = formatDateForApi(updateData.startDate)
      }
      if (updateData.dueDate) {
        updateData.dueDate = formatDateForApi(updateData.dueDate)
      }

      // 移除空值字段
      const cleanUpdateData = removeEmptyFields(updateData)
      console.log('[TodoTool] [updateTask] 准备发送的更新数据：', cleanUpdateData)

      // 发送更新请求
      const response = await this._request('POST', `${API_CONFIG.TASK_URL}/${taskId}`, cleanUpdateData)
      const parsedResponse = parseHttpResponse(response)

      if (parsedResponse.errCode) {
        console.error('[TodoTool] [updateTask] 更新任务失败：', parsedResponse)
        return parsedResponse
      }

      // 清除缓存
      this.cache.delete('batchData')

      console.log('[TodoTool] [updateTask] 任务更新成功')
      return createSuccessResponse('任务更新成功', parsedResponse.data)

    } catch (error) {
      console.error('[TodoTool] [updateTask] 更新任务异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '更新任务失败', error)
    }
  }

  /**
   * 缓存管理：清理过期缓存
   */
  _cleanExpiredCache() {
    const now = Date.now()
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.cache.delete(key)
      }
    }

    // 如果缓存条目过多，删除最旧的条目
    if (this.cache.size > this.maxCacheSize) {
      const entries = Array.from(this.cache.entries())
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
      const toDelete = entries.slice(0, entries.length - this.maxCacheSize)
      toDelete.forEach(([key]) => this.cache.delete(key))
    }
  }

  /**
   * 性能统计：更新统计信息
   * @param {number} responseTime - 响应时间
   * @param {boolean} cacheHit - 是否命中缓存
   */
  _updateStats(responseTime, cacheHit = false) {
    this.stats.requestCount++
    if (cacheHit) {
      this.stats.cacheHits++
    } else {
      this.stats.cacheMisses++
    }

    // 计算平均响应时间
    this.stats.avgResponseTime =
      (this.stats.avgResponseTime * (this.stats.requestCount - 1) + responseTime) / this.stats.requestCount
  }

  /**
   * 获取性能统计信息
   * @returns {object} 性能统计数据
   */
  getPerformanceStats() {
    const now = Date.now()
    const uptime = now - this.stats.lastResetTime

    return {
      ...this.stats,
      uptime: uptime,
      cacheHitRate: this.stats.requestCount > 0 ?
        (this.stats.cacheHits / this.stats.requestCount * 100).toFixed(2) + '%' : '0%',
      cacheSize: this.cache.size,
      isAuthenticated: this.isAuthenticated
    }
  }

  /**
   * 统一的 HTTP 请求方法
   * 实现连接复用和错误处理
   * @param {string} method - HTTP 方法
   * @param {string} url - 请求 URL
   * @param {object} data - 请求数据
   * @param {object} customHeaders - 自定义请求头
   * @returns {object} 响应结果
   */
  async _request(method, url, data = null, customHeaders = {}) {
    const startTime = Date.now()
    const fullUrl = url.startsWith('http') ? url : `${this.BASE_URL}${url}`
    const headers = { ...this.headers, ...customHeaders }

    const requestOptions = {
      method: method,
      headers: headers,
      timeout: API_CONFIG.TIMEOUT
    }

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      requestOptions.data = data
    }

    try {
      console.log(`[TodoTool] [_request] ${method} ${fullUrl}`)
      const response = await uniCloud.httpclient.request(fullUrl, requestOptions)

      // 更新性能统计
      const responseTime = Date.now() - startTime
      this._updateStats(responseTime, false)

      return response
    } catch (error) {
      console.error(`[TodoTool] [_request] 请求失败：`, error)

      // 更新性能统计（失败请求）
      const responseTime = Date.now() - startTime
      this._updateStats(responseTime, false)

      throw error
    }
  }
}

// 将扩展方法添加到 TodoTool 原型中
Object.assign(TodoTool.prototype, taskMethods, projectMethods)

module.exports = TodoTool
