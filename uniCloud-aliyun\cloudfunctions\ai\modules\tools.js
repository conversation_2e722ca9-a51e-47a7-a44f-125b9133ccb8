// 工具调用模块
// 负责实际调用云函数工具、内置工具和模拟工具调用

const { TOOL_REGISTRY } = require('./config')

// 内置工具实例缓存
const builtinTools = new Map()

// 获取内置工具实例
function getBuiltinTool(toolName) {
  if (!builtinTools.has(toolName)) {
    try {
      // 根据工具名称动态加载对应的内置工具类
      let ToolClass
      switch (toolName) {
        case 'todo':
          ToolClass = require('./todo-tool')
          break
        default:
          throw new Error(`未知的内置工具：${toolName}`)
      }

      // 创建工具实例并缓存
      const toolInstance = new ToolClass()
      builtinTools.set(toolName, toolInstance)
    } catch (error) {
      throw new Error(`加载内置工具失败：${error.message}`)
    }
  }

  return builtinTools.get(toolName)
}

// 真实工具调用函数
async function callRealTool(toolName, parameters) {
  const toolConfig = TOOL_REGISTRY[toolName]
  if (!toolConfig) {
    throw new Error(`未找到工具：${toolName}`)
  }

  try {
    // 判断工具类型并调用相应的处理逻辑
    if (toolConfig.type === 'builtin') {
      // 内置工具调用
      console.log(`[callRealTool] 调用内置工具：${toolConfig.tool}.${toolConfig.method}`)

      const toolInstance = getBuiltinTool(toolConfig.tool)
      const result = await toolInstance.execute(toolConfig.method, parameters)

      return result
    } else {
      // 云函数工具调用（兼容旧版本）
      console.log(`[callRealTool] 调用云函数工具：${toolConfig.cloudFunction}.${toolConfig.method}`)

      const cloudFunction = uniCloud.importObject(toolConfig.cloudFunction)
      const result = await cloudFunction[toolConfig.method](parameters)

      return result
    }
  } catch (error) {
    console.error(`[callRealTool] 工具执行失败：`, error)
    throw new Error(`工具执行失败：${error.message}`)
  }
}

// 模拟工具调用（仅用于测试环境）
// 注意：此函数已不在生产环境使用，仅保留用于测试模块
async function simulateToolCall(toolName, parameters) {
  // 模拟延迟
  await new Promise((resolve) => setTimeout(resolve, 1000))

  switch (toolName) {
    case 'getProjects':
      return {
        success: true,
        data: [
          { id: 'proj-1', name: 'OKR 项目', description: '目标管理项目' },
          { id: 'proj-2', name: '日常任务', description: '日常工作任务' },
        ],
        metadata: { total: 2, filtered: parameters.filter ? 1 : 2 },
      }

    case 'getTasks':
      return {
        success: true,
        tasks: [
          { id: 'task-1', title: '制定 Q1 目标', completed: false, projectId: parameters.projectId || 'proj-1' },
          { id: 'task-2', title: '更新 KR 进度', completed: false, projectId: parameters.projectId || 'proj-1' },
        ],
        metadata: { total: 2, projectId: parameters.projectId },
      }

    default:
      throw new Error(`未知的工具：${toolName}`)
  }
}

// 获取上下文更新信息
function getContextUpdates(context) {
  const updates = {}
  for (const [key, value] of context.contextData.entries()) {
    updates[key] = value
  }
  return updates
}

// 生成执行摘要
function generateExecutionSummary(executionPlan, context) {
  const completedSteps = executionPlan.steps.filter((s) => s.status === 'completed')
  const failedSteps = executionPlan.steps.filter((s) => s.status === 'failed')

  return {
    totalSteps: executionPlan.totalSteps,
    completedSteps: completedSteps.length,
    failedSteps: failedSteps.length,
    totalExecutionTime: executionPlan.totalExecutionTime,
    averageStepTime:
      completedSteps.length > 0
        ? Math.round(completedSteps.reduce((sum, s) => sum + s.executionTime, 0) / completedSteps.length)
        : 0,
    contextDataKeys: Array.from(context.contextData.keys()),
    success: failedSteps.length === 0,
  }
}

// 辅助函数：计算动态引用数量
function countDynamicReferences(parameters) {
  let count = 0
  const paramStr = JSON.stringify(parameters)

  // 计算各种动态引用
  count += (paramStr.match(/\$context\./g) || []).length
  count += (paramStr.match(/\$step\./g) || []).length
  count += (paramStr.match(/\$filter\(/g) || []).length

  return count
}

module.exports = {
  callRealTool,
  simulateToolCall,
  getContextUpdates,
  generateExecutionSummary,
  countDynamicReferences,
}
